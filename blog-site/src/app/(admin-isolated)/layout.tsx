import { Inter } from "next/font/google"
import "./admin-globals.css"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
})

export const metadata = {
  title: "Admin Panel - शायरी ब्लॉग",
  description: "Admin panel for managing blog content",
}

export default function AdminIsolatedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased admin-isolated`}>
        {children}
      </body>
    </html>
  )
}
